"""
Face Recognition Engine
Integrated face recognition system for PPE monitoring with dataset management and training capabilities
"""

import cv2
import numpy as np
import os
import logging
from typing import Dict, List, Tuple, Optional
import pickle
from datetime import datetime
import json


class FaceRecognitionEngine:
    """Face recognition engine with dataset management and training capabilities"""
    
    def __init__(self, dataset_path: str = "face_dataset", model_path: str = "face_model.pkl"):
        """Initialize the face recognition engine
        
        Args:
            dataset_path: Path to store face dataset images
            model_path: Path to save/load the trained model
        """
        self.dataset_path = dataset_path
        self.model_path = model_path
        self.cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        
        # Initialize face detector
        self.face_cascade = cv2.CascadeClassifier(self.cascade_path)
        if self.face_cascade.empty():
            logging.error(f"Failed to load face cascade from {self.cascade_path}")
            raise RuntimeError("Face cascade classifier not found")
        
        # Initialize face recognizer
        self.face_recognizer = cv2.face.LBPHFaceRecognizer_create()
        self.is_trained = False
        self.known_faces = {}  # person_id -> name mapping
        self.confidence_threshold = 82  # Confidence threshold for recognition
        
        # Create dataset directory if it doesn't exist
        os.makedirs(self.dataset_path, exist_ok=True)
        
        # Load existing model if available
        self.load_model()
    
    def extract_face(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Extract face from image
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Extracted face image or None if no face found
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        
        if len(faces) == 0:
            return None
        
        # Return the largest face (assuming it's the main subject)
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        x, y, w, h = largest_face
        
        return image[y:y+h, x:x+w]
    
    def detect_faces(self, image: np.ndarray) -> List[Dict]:
        """Detect all faces in an image and return their locations
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of face detection dictionaries
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        
        face_detections = []
        for i, (x, y, w, h) in enumerate(faces):
            face_detections.append({
                'bbox': [x, y, x+w, y+h],
                'confidence': 1.0,  # Haar cascade doesn't provide confidence
                'face_id': i,
                'recognized_person': None,
                'recognition_confidence': 0.0
            })
        
        return face_detections
    
    def recognize_faces(self, image: np.ndarray) -> List[Dict]:
        """Detect and recognize faces in an image
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of face recognition results
        """
        if not self.is_trained:
            # Return just face detections without recognition
            return self.detect_faces(image)
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        
        face_results = []
        for i, (x, y, w, h) in enumerate(faces):
            # Extract face region
            face_roi = gray[y:y+h, x:x+w]
            face_resized = cv2.resize(face_roi, (200, 200))
            
            # Perform recognition
            person_id, confidence = self.face_recognizer.predict(face_resized)
            
            # Calculate recognition confidence (higher is better)
            recognition_confidence = int(100 * (1 - (confidence / 300))) if confidence < 500 else 0
            
            # Determine if person is recognized
            recognized_person = None
            if recognition_confidence > self.confidence_threshold:
                recognized_person = self.known_faces.get(person_id, f"Person_{person_id}")
            
            face_results.append({
                'bbox': [x, y, x+w, y+h],
                'confidence': 1.0,  # Detection confidence
                'face_id': i,
                'recognized_person': recognized_person,
                'recognition_confidence': recognition_confidence,
                'person_id': person_id if recognized_person else None
            })
        
        return face_results
    
    def train_model(self) -> Dict:
        """Train the face recognition model with collected samples
        
        Returns:
            Dictionary with training results
        """
        training_data = []
        labels = []
        person_names = {}
        
        results = {
            'status': 'started',
            'people_trained': 0,
            'total_samples': 0,
            'people_names': []
        }
        
        try:
            # Scan dataset directory for person folders
            person_id = 0
            for person_name in os.listdir(self.dataset_path):
                person_dir = os.path.join(self.dataset_path, person_name)
                
                if not os.path.isdir(person_dir):
                    continue
                
                person_samples = 0
                
                # Load all images for this person
                for filename in os.listdir(person_dir):
                    if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                        image_path = os.path.join(person_dir, filename)
                        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
                        
                        if image is not None:
                            # Resize to standard size
                            image_resized = cv2.resize(image, (200, 200))
                            training_data.append(image_resized)
                            labels.append(person_id)
                            person_samples += 1
                
                if person_samples > 0:
                    person_names[person_id] = person_name
                    results['people_names'].append(person_name)
                    person_id += 1
                    results['total_samples'] += person_samples
            
            if len(training_data) == 0:
                results['status'] = 'error'
                results['error'] = 'No training data found'
                return results
            
            # Train the model
            labels = np.array(labels, dtype=np.int32)
            self.face_recognizer.train(training_data, labels)
            
            # Save the model and person names
            self.known_faces = person_names
            self.is_trained = True
            self.save_model()
            
            results['status'] = 'completed'
            results['people_trained'] = len(person_names)
            
        except Exception as e:
            results['status'] = 'error'
            results['error'] = str(e)
            logging.error(f"Error during model training: {e}")
        
        return results
    
    def save_model(self) -> bool:
        """Save the trained model and metadata
        
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if self.is_trained:
                # Save the OpenCV model
                model_xml_path = self.model_path.replace('.pkl', '.xml')
                self.face_recognizer.save(model_xml_path)
                
                # Save metadata
                metadata = {
                    'known_faces': self.known_faces,
                    'confidence_threshold': self.confidence_threshold,
                    'is_trained': self.is_trained,
                    'training_date': datetime.now().isoformat()
                }
                
                with open(self.model_path, 'wb') as f:
                    pickle.dump(metadata, f)
                
                return True
        except Exception as e:
            logging.error(f"Error saving model: {e}")
        
        return False
    
    def load_model(self) -> bool:
        """Load the trained model and metadata
        
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            # Load metadata
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    metadata = pickle.load(f)
                
                self.known_faces = metadata.get('known_faces', {})
                self.confidence_threshold = metadata.get('confidence_threshold', 82)
                self.is_trained = metadata.get('is_trained', False)
                
                # Load the OpenCV model
                model_xml_path = self.model_path.replace('.pkl', '.xml')
                if os.path.exists(model_xml_path) and self.is_trained:
                    self.face_recognizer.read(model_xml_path)
                    return True
        except Exception as e:
            logging.error(f"Error loading model: {e}")
        
        return False
    
    def get_dataset_info(self) -> Dict:
        """Get information about the current dataset
        
        Returns:
            Dictionary with dataset information
        """
        info = {
            'dataset_path': self.dataset_path,
            'people': [],
            'total_people': 0,
            'total_samples': 0,
            'is_trained': self.is_trained
        }
        
        try:
            for person_name in os.listdir(self.dataset_path):
                person_dir = os.path.join(self.dataset_path, person_name)
                
                if os.path.isdir(person_dir):
                    sample_count = len([f for f in os.listdir(person_dir) 
                                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                    
                    info['people'].append({
                        'name': person_name,
                        'samples': sample_count
                    })
                    info['total_samples'] += sample_count
            
            info['total_people'] = len(info['people'])
        except Exception as e:
            logging.error(f"Error getting dataset info: {e}")
        
        return info

    def draw_face_detections(self, image: np.ndarray, face_results: List[Dict],
                           show_confidence: bool = True) -> np.ndarray:
        """Draw face detection and recognition results on image

        Args:
            image: Input image as numpy array
            face_results: List of face detection/recognition results
            show_confidence: Whether to show confidence scores

        Returns:
            Image with drawn detections
        """
        result_image = image.copy()

        for face in face_results:
            x1, y1, x2, y2 = face['bbox']

            # Determine color based on recognition status
            if face['recognized_person']:
                color = (0, 255, 0)  # Green for recognized
                label = face['recognized_person']
                if show_confidence:
                    label += f" ({face['recognition_confidence']:.0f}%)"
            else:
                color = (0, 0, 255)  # Red for unknown
                label = "Unknown Person"

            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)

            # Draw label background
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), color, -1)

            # Draw label text
            cv2.putText(result_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return result_image

    def delete_person_data(self, person_name: str) -> bool:
        """Delete all data for a specific person

        Args:
            person_name: Name of the person to delete

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            person_dir = os.path.join(self.dataset_path, person_name)
            if os.path.exists(person_dir):
                import shutil
                shutil.rmtree(person_dir)

                # Remove from known faces if present
                person_id_to_remove = None
                for person_id, name in self.known_faces.items():
                    if name == person_name:
                        person_id_to_remove = person_id
                        break

                if person_id_to_remove is not None:
                    del self.known_faces[person_id_to_remove]
                    self.save_model()

                return True
        except Exception as e:
            logging.error(f"Error deleting person data: {e}")

        return False

    def update_confidence_threshold(self, threshold: int) -> None:
        """Update the confidence threshold for recognition

        Args:
            threshold: New confidence threshold (0-100)
        """
        self.confidence_threshold = max(0, min(100, threshold))
        if self.is_trained:
            self.save_model()

    def get_person_sample_count(self, person_name: str) -> int:
        """Get the number of samples for a specific person

        Args:
            person_name: Name of the person

        Returns:
            Number of samples for the person
        """
        try:
            person_dir = os.path.join(self.dataset_path, person_name)
            if os.path.exists(person_dir):
                return len([f for f in os.listdir(person_dir)
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
        except Exception as e:
            logging.error(f"Error getting sample count for {person_name}: {e}")
        return 0

    def clear_all_data(self) -> bool:
        """Clear all face recognition data including dataset and model

        Returns:
            True if cleared successfully, False otherwise
        """
        try:
            import shutil

            # Remove dataset directory
            if os.path.exists(self.dataset_path):
                shutil.rmtree(self.dataset_path)
                os.makedirs(self.dataset_path, exist_ok=True)

            # Remove model files
            if os.path.exists(self.model_path):
                os.remove(self.model_path)

            model_xml_path = self.model_path.replace('.pkl', '.xml')
            if os.path.exists(model_xml_path):
                os.remove(model_xml_path)

            # Reset engine state
            self.known_faces = {}
            self.is_trained = False

            return True
        except Exception as e:
            logging.error(f"Error clearing all data: {e}")
            return False

    def test_camera(self) -> Dict:
        """Test camera connectivity and basic functionality

        Returns:
            Dictionary with test results
        """
        result = {
            'status': 'success',
            'camera_accessible': False,
            'resolution': None,
            'error': None
        }

        try:
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                result['camera_accessible'] = True

                # Get camera resolution
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                result['resolution'] = f"{width}x{height}"

                # Test frame capture
                ret, frame = cap.read()
                if not ret:
                    result['status'] = 'warning'
                    result['error'] = 'Camera accessible but cannot capture frames'

                cap.release()
            else:
                result['status'] = 'error'
                result['error'] = 'Camera not accessible'

        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)

        return result

    def get_recognition_stats(self, face_results: List[Dict]) -> Dict:
        """Get statistics about face recognition results

        Args:
            face_results: List of face recognition results

        Returns:
            Dictionary with recognition statistics
        """
        stats = {
            'total_faces': len(face_results),
            'recognized_faces': 0,
            'unknown_faces': 0,
            'recognized_people': [],
            'recognition_rate': 0.0
        }

        for face in face_results:
            if face['recognized_person']:
                stats['recognized_faces'] += 1
                if face['recognized_person'] not in stats['recognized_people']:
                    stats['recognized_people'].append(face['recognized_person'])
            else:
                stats['unknown_faces'] += 1

        if stats['total_faces'] > 0:
            stats['recognition_rate'] = (stats['recognized_faces'] / stats['total_faces']) * 100

        return stats

    def collect_face_samples(self, person_name: str, num_samples: int = 100) -> Dict:
        """Collect face samples from camera for a specific person

        Args:
            person_name: Name of the person to collect samples for
            num_samples: Number of samples to collect (default: 100)

        Returns:
            Dictionary with collection results
        """
        results = {
            'status': 'started',
            'samples_collected': 0,
            'person_name': person_name,
            'error': None
        }

        try:
            # Create person directory
            person_dir = os.path.join(self.dataset_path, person_name)
            os.makedirs(person_dir, exist_ok=True)

            # Initialize camera
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                results['status'] = 'error'
                results['error'] = 'Could not open camera'
                return results

            count = 0
            consecutive_no_face = 0
            max_no_face_frames = 30  # Stop if no face detected for 30 consecutive frames

            logging.info(f"Starting face collection for {person_name}. Target: {num_samples} samples")

            while count < num_samples:
                ret, frame = cap.read()
                if not ret:
                    results['status'] = 'error'
                    results['error'] = 'Failed to read from camera'
                    break

                # Extract face from frame
                face = self.extract_face(frame)

                if face is not None:
                    consecutive_no_face = 0
                    count += 1

                    # Resize and convert to grayscale
                    face_resized = cv2.resize(face, (200, 200))
                    face_gray = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)

                    # Save the face sample
                    file_name_path = os.path.join(person_dir, f"{count}.jpg")
                    cv2.imwrite(file_name_path, face_gray)

                    # Display progress on the face
                    cv2.putText(face_resized, f"{count}/{num_samples}", (10, 30),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.imshow(f'Collecting samples for {person_name}', face_resized)

                    logging.debug(f"Collected sample {count}/{num_samples} for {person_name}")
                else:
                    consecutive_no_face += 1
                    # Display "No face detected" message
                    display_frame = frame.copy()
                    cv2.putText(display_frame, "No face detected", (50, 50),
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                    cv2.putText(display_frame, f"Samples: {count}/{num_samples}", (50, 100),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                    cv2.putText(display_frame, "Press 'q' to quit", (50, 150),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                    cv2.imshow(f'Collecting samples for {person_name}', display_frame)

                # Check for user interruption
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == 27:  # 'q' or ESC key
                    results['status'] = 'interrupted'
                    break

                # Stop if no face detected for too long
                if consecutive_no_face > max_no_face_frames:
                    results['status'] = 'error'
                    results['error'] = f'No face detected for {max_no_face_frames} consecutive frames'
                    break

            # Clean up
            cap.release()
            cv2.destroyAllWindows()

            results['samples_collected'] = count

            if results['status'] == 'started':  # No interruption or error
                results['status'] = 'completed'
                logging.info(f"Successfully collected {count} samples for {person_name}")

        except Exception as e:
            results['status'] = 'error'
            results['error'] = str(e)
            logging.error(f"Error during face sample collection: {e}")

            # Clean up on error
            try:
                cap.release()
                cv2.destroyAllWindows()
            except:
                pass

        return results
